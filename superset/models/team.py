# DODO added #32839638
import sqlalchemy as sa
from flask_appbuilder import Model
from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    Column,
    Foreign<PERSON>ey,
    Integer,
    String,
    Table,
)
from sqlalchemy.orm import relationship

from superset import security_manager

metadata = Model.metadata  # pylint: disable=no-member

team_roles = Table(
    "team_roles",
    metadata,
    Column("id", Integer, primary_key=True),
    Column(
        "team_id",
        Integer,
        ForeignKey("teams.id", ondelete="CASCADE"),
        nullable=False,
    ),
    <PERSON>umn(
        "role_id",
        Integer,
        ForeignKey("ab_role.id", ondelete="CASCADE"),
        nullable=False,
    ),
)


team_users = Table(
    "team_users",
    metadata,
    Column("id", Integer, primary_key=True),
    Column("team_id", Integer, ForeignKey("teams.id", ondelete="CASCADE")),
    <PERSON>umn("user_id", <PERSON>te<PERSON>, <PERSON><PERSON><PERSON>("ab_user.id", ondelete="CASCADE")),
)


class Team(Model):
    """Dodo teams for Superset"""

    __tablename__ = "teams"

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    is_external = Column(Boolean, nullable=False)
    is_consumer = Column(
        Boolean,
        nullable=False,
        default=True,
        server_default=sa.true(),
    )
    slug = Column(String, unique=True)
    tag_id = Column(Integer, ForeignKey("tag.id"), nullable=True)
    roles = relationship(security_manager.role_model, secondary=team_roles)
    participants = relationship(
        security_manager.user_model,
        secondary=team_users,
        passive_deletes=True,
        backref="teams",
    )

    def __repr__(self) -> str:
        return self.name
