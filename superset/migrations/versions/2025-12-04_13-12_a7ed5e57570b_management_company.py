# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
"""Management company

Revision ID: a7ed5e57570b
Revises: b9d3a202f14a
Create Date: 2025-12-04 13:12:18.766385

"""

import sqlalchemy as sa
from alembic import op
from flask_appbuilder.security.sqla.models import assoc_user_role
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session

from superset.extensions import security_manager

revision = "a7ed5e57570b"
down_revision = "b9d3a202f14a"

Base = declarative_base()


class UserInfo(Base):
    """Local UserInfo model for migration"""

    __tablename__ = "user_info"

    id = sa.Column(sa.Integer, primary_key=True)
    user_id = sa.Column(sa.Integer, sa.ForeignKey("ab_user.id"))
    is_management = sa.Column(sa.Boolean, nullable=False, default=False)


class UserLocal(Base):
    """Local User model for migration"""

    __tablename__ = "ab_user"

    id = sa.Column(sa.Integer, primary_key=True)
    email = sa.Column(sa.String)


MC_ROLE = "MC"
NON_MC_ROLE = "Non MC"


def upgrade():
    # Add is_management column to user_info table
    op.add_column(
        "user_info",
        sa.Column(
            "is_management", sa.Boolean(), nullable=False, server_default="false"
        ),
    )

    bind = op.get_bind()
    session = Session(bind=bind)

    try:
        mc_role = security_manager.find_role(MC_ROLE)
        if not mc_role:
            mc_role = security_manager.add_role(MC_ROLE)
            session.flush()

        non_mc_role = security_manager.find_role(NON_MC_ROLE)
        if not non_mc_role:
            non_mc_role = security_manager.add_role(NON_MC_ROLE)
            session.flush()

        # Collect user IDs
        all_user_ids = [uid for (uid,) in session.query(UserLocal.id).all()]

        management_ids = [
            uid
            for (uid,) in session.query(UserLocal.id)
            .filter(
                sa.or_(
                    sa.func.lower(UserLocal.email).like("%@dodobrands.io"),
                    sa.func.lower(UserLocal.email).like("%@dodopizza.%"),
                )
            )
            .all()
        ]
        non_management_ids = list(set(all_user_ids) - set(management_ids))

        # Update user_info.is_management in bulk (ORM)
        if management_ids:
            session.query(UserInfo).filter(UserInfo.user_id.in_(management_ids)).update(
                {"is_management": True}, synchronize_session=False
            )
        if non_management_ids:
            session.query(UserInfo).filter(
                UserInfo.user_id.in_(non_management_ids)
            ).update({"is_management": False}, synchronize_session=False)

        # Assign MC role to management users
        if management_ids:
            existing_mc = {
                uid
                for (uid,) in session.query(assoc_user_role.c.user_id)
                .filter(
                    assoc_user_role.c.role_id == mc_role.id,
                    assoc_user_role.c.user_id.in_(management_ids),
                )
                .all()
            }
            missing_mc = set(management_ids) - existing_mc
            if missing_mc:
                print(f"Adding MC role to {len(missing_mc)} users")
                session.execute(
                    assoc_user_role.insert(),
                    [{"user_id": uid, "role_id": mc_role.id} for uid in missing_mc],
                )

        # Assign Non MC role to non-management users
        if non_management_ids:
            existing_non_mc = {
                uid
                for (uid,) in session.query(assoc_user_role.c.user_id)
                .filter(
                    assoc_user_role.c.role_id == non_mc_role.id,
                    assoc_user_role.c.user_id.in_(non_management_ids),
                )
                .all()
            }
            missing_non_mc = set(non_management_ids) - existing_non_mc
            if missing_non_mc:
                print(f"Adding Non MC role to {len(missing_non_mc)} users")
                session.execute(
                    assoc_user_role.insert(),
                    [
                        {"user_id": uid, "role_id": non_mc_role.id}
                        for uid in missing_non_mc
                    ],
                )

        session.commit()
    finally:
        session.close()


def downgrade():
    op.drop_column("user_info", "is_management")
