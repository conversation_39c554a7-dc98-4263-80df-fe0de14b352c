# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
"""Add is consumer to teams

Revision ID: bee7b2223e22
Revises: a7ed5e57570b
Create Date: 2025-12-10 07:47:33.473226

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session

# revision identifiers, used by Alembic.
revision = "bee7b2223e22"
down_revision = "a7ed5e57570b"

Base = declarative_base()


class Team(Base):
    """Local Team model for migration"""

    __tablename__ = "teams"

    id = sa.Column(sa.Integer, primary_key=True)
    name = sa.Column(sa.String, nullable=False)
    is_external = sa.Column(sa.Boolean, nullable=False)
    slug = sa.Column(sa.String, unique=True)
    is_consumer = sa.Column(sa.Boolean, nullable=False)


def upgrade():
    op.add_column(
        "teams",
        sa.Column(
            "is_consumer",
            sa.Boolean(),
            nullable=False,
            server_default=sa.true(),
        ),
    )

    bind = op.get_bind()
    session = Session(bind=bind)

    try:
        teams_to_update = (
            session.query(Team).filter(Team.slug.ilike("%analytics")).all()
        )

        for team in teams_to_update:
            print(f"Updating team {team.name} to is_consumer=False")
            team.is_consumer = False

        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


def downgrade():
    op.drop_column("teams", "is_consumer")
