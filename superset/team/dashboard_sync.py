import logging

from superset.daos.dashboard import DashboardDAO
from superset.daos.user import Use<PERSON><PERSON><PERSON>
from superset.models.dashboard import Dashboard
from superset.models.team import Team

logger = logging.getLogger(__name__)


def sync_dashboard_owners(
    dashboard: Dashboard,
    team: Team,
    previous_team_member_ids: set[int] | None = None,
    current_team_member_ids: set[int] | None = None,
) -> None:
    """
    Sync dashboard owners with team participants.

    dashboard: Dashboard - the dashboard to sync owners for
    team: Team - the team to sync owners for
    previous_team_member_ids: set[int] - the IDs of the team members before the sync
    current_team_member_ids: set[int] - the IDs of the team members after the sync
    """

    if team.is_consumer:
        return

    current_member_ids: set[int] = (
        set(current_team_member_ids)
        if current_team_member_ids is not None
        else {user.id for user in team.participants}
    )
    previous_member_ids: set[int] = set(previous_team_member_ids or set())

    owner_ids = {owner.id for owner in dashboard.owners}

    to_add_ids = list(current_member_ids - owner_ids)
    for user in UserDAO.find_by_ids(to_add_ids, skip_base_filter=True):
        if user not in dashboard.owners:
            dashboard.owners.append(user)

    if to_remove_ids := previous_member_ids - current_member_ids:
        dashboard.owners = [
            owner for owner in dashboard.owners if owner.id not in to_remove_ids
        ]


def sync_dashboards_for_team(
    team: Team, previous_team_member_ids: set[int] | None = None
) -> None:
    """
    Sync owners for all dashboards that have the team role assigned.
    """

    if team.is_consumer:
        return

    dashboards = DashboardDAO.find_by_team_role(team.name)
    for dashboard in dashboards:
        sync_dashboard_owners(
            dashboard,
            team,
            previous_team_member_ids=previous_team_member_ids,
        )
