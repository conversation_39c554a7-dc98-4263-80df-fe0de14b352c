# DODO was here
from __future__ import annotations

import datetime
import functools
import logging
import math
from typing import Any, TYPE_CHECKING

import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)

if TYPE_CHECKING:
    from superset.common.query_object import QueryObject


def left_join_df(
    left_df: pd.DataFrame,
    right_df: pd.DataFrame,
    join_keys: list[str],
    lsuffix: str = "",
    rsuffix: str = "",
) -> pd.DataFrame:
    df = left_df.set_index(join_keys).join(
        right_df.set_index(join_keys), lsuffix=lsuffix, rsuffix=rsuffix
    )
    df.reset_index(inplace=True)
    return df


def full_outer_join_df(
    left_df: pd.DataFrame,
    right_df: pd.DataFrame,
    lsuffix: str = "",
    rsuffix: str = "",
) -> pd.DataFrame:
    df = left_df.join(right_df, lsuffix=lsuffix, rsuffix=rsuffix, how="outer")
    df.reset_index(inplace=True)
    return df


def df_metrics_to_num(df: pd.DataFrame, query_object: QueryObject) -> None:
    """Converting metrics to numeric when pandas.read_sql cannot"""
    for col, dtype in df.dtypes.items():
        if dtype.type == np.object_ and col in query_object.metric_names:
            # soft-convert a metric column to numeric
            df[col] = df[col].infer_objects()


def is_datetime_series(series: Any) -> bool:
    if series is None or not isinstance(series, pd.Series):
        return False

    if series.isnull().all():
        return False

    return pd.api.types.is_datetime64_any_dtype(series) or (
        series.apply(lambda x: isinstance(x, datetime.date) or x is None).all()
    )


# DODO added start: CSV/XLSX export logic #52459208
# pylint: disable=logging-fstring-interpolation,too-many-branches
def convert_to_time(value: Any) -> str:
    from decimal import Decimal  # pylint: disable=import-outside-toplevel

    # Handle None/NaN cases
    if value is None:
        logger.warning("Export Debug - Value is None, returning 00:00:00")
        return "00:00:00"

    # Handle string values that might be numeric
    if isinstance(value, str):
        # If it's already formatted as time, return as-is
        if ":" in value:
            return value
        # Try to convert string to number
        try:
            value = float(value)
        except (ValueError, TypeError):
            logger.warning(
                f"Export Debug - Could not convert string to number: {value}"
            )
            return value

    # Handle Decimal values (DODO added fix for decimal.Decimal)
    if isinstance(value, Decimal):
        try:
            value = float(value)
        except (ValueError, TypeError):
            logger.warning(
                f"Export Debug - Could not convert Decimal to float: {value}"
            )
            return "00:00:00"

    # Handle numeric values (int, float, or converted Decimal)
    if isinstance(value, (int, float)):
        # Check for NaN
        if math.isnan(value):
            logger.warning("Export Debug - Value is NaN, returning 00:00:00")
            return "00:00:00"

        # DODO fixed: Always treat duration values as milliseconds for consistency with UI
        # The UI displays duration values by treating them as milliseconds
        # So export should do the same to maintain consistency
        total_seconds = int(value // 1000)

        hours, remainder = divmod(total_seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        result = f"{hours:02}:{minutes:02}:{seconds:02}"
        return result

    # Fallback for other types
    logger.warning(
        f"Export Debug - Unexpected value type: {type(value)}, returning 00:00:00"
    )
    return "00:00:00"


# DODO added stop: CSV/XLSX export logic #52459208


# DODO added: rich duration formatting to mirror frontend D3 duration formats #53925382
def _safe_number(value: Any) -> float | None:  # pylint: disable=too-many-return-statements
    """Best-effort conversion to float, None on failure."""
    from decimal import Decimal  # pylint: disable=import-outside-toplevel

    if value is None:
        return None
    # numpy numeric scalars
    try:
        import numpy as _np  # pylint: disable=import-outside-toplevel,reimported

        if isinstance(value, (_np.integer, _np.floating)):
            try:
                f = float(value)
                if math.isnan(f):
                    return None
                return f
            except Exception:  # pylint: disable=broad-except
                return None
    except Exception:  # pylint: disable=broad-except
        pass

    if isinstance(value, (int, float)):
        try:
            if math.isnan(value):
                return None
        except Exception:  # pylint: disable=broad-except
            pass
        return float(value)
    if isinstance(value, str):
        try:
            return float(value)
        except Exception:  # pylint: disable=broad-except
            return None
    if isinstance(value, Decimal):
        try:
            return float(value)
        except Exception:  # pylint: disable=broad-except
            return None
    return None


def _format_hmmss(total_seconds: int) -> str:
    hours, remainder = divmod(max(total_seconds, 0), 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{hours}:{minutes:02}:{seconds:02}"


def _format_human_hms(total_seconds: int) -> str:
    total_seconds = max(total_seconds, 0)
    hours, remainder = divmod(total_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    parts: list[str] = []
    if hours:
        parts.append(f"{hours}h")
    if minutes:
        parts.append(f"{minutes}m")
    if seconds or not parts:
        parts.append(f"{seconds}s")
    return " ".join(parts)


def format_duration_by_d3format(value: Any, d3format: str) -> str:
    """Dispatch duration formatting based on D3 format token from frontend.

    Supported tokens:
      - DURATION (ms) / DURATION_SECONDS (s) -> "1m 6s" or "2h 15m 30s"
      - DURATION_HMMSS (ms) / DURATION_HMMSS_SECONDS (s) -> "H:MM:SS"
    """
    number = _safe_number(value)
    if number is None:
        return "00:00:00"

    token = (d3format or "").upper()

    is_seconds = "SECONDS" in token
    value_seconds = float(number) if is_seconds else float(number) / 1000.0

    if "DURATION_HMMSS" in token:
        return _format_hmmss(int(value_seconds))

    if "DURATION" in token:
        return _format_human_hms(int(value_seconds))

    return "00:00:00"


# DODO added start:CSV/XLSX export logic #52459208
def format_data_for_export(
    df: pd.DataFrame,
    form_data: dict[str, Any] | None = None,
    column_translations: dict[str, str] | None = None,  # pylint: disable=unused-argument
    sorting_column_mapping: dict[str, str] | None = None,
) -> pd.DataFrame:
    form_data = form_data or {}

    export_as_time = form_data.get("export_as_time")
    table_order_by = form_data.get("table_order_by", {})

    if export_as_time and not form_data.get(
        "column_config"
    ):  # экспорт в формате времени
        key_column = df.keys()[0]
        df[key_column] = df[key_column].apply(convert_to_time)

    for column in df.columns:
        if pd.api.types.is_datetime64tz_dtype(df[column]):
            df[column] = df[column].dt.tz_localize(None)

    # DODO added: exportAsTime logic moved to apply_export_as_time_after_translation for stability
    # The old logic here was unreliable due to column name mismatches
    # Now handled by stable query metadata mapping in apply_export_as_time_after_translation

    # Сортировка данных с поддержкой переводов
    sorting_column_mapping = sorting_column_mapping or {}

    # Always reset index first to ensure clean export (removes original row indices)
    # This is crucial when data comes pre-sorted from database queries
    if not isinstance(df.index, pd.RangeIndex):
        df.reset_index(drop=True, inplace=True)

    # Only apply user sorting if table_order_by is not empty
    # If empty, preserve the default sorting from database query
    if table_order_by:
        for column, order in table_order_by.items():
            # First try to find the column directly
            sort_column = None
            if column in df.columns:
                sort_column = column
            # If not found, try to find the actual DataFrame column using sorting mapping
            elif column in sorting_column_mapping:
                sort_column = sorting_column_mapping[column]

            if sort_column:
                df.sort_values(
                    by=[sort_column], ascending=(order == "asc"), inplace=True
                )
                # Reset index after sorting to ensure proper row ordering in export
                df.reset_index(drop=True, inplace=True)
            else:
                logger.warning(
                    f"Export Debug - Could not find column for sorting: '{column}'. Available columns: {list(df.columns)}"
                )

    return df
    # DODO added stop:CSV/XLSX export logic #52459208


# DODO added start: CSV/XLSX export logic #52459208
def _apply_d3_format(value: Any, d3format: str, for_csv: bool = False) -> str:  # pylint: disable=unused-argument
    """
    Apply D3 number format to a value.
    Supports duration formats: DURATION, DURATION_SECONDS, DURATION_HMMSS, DURATION_HMMSS_SECONDS.
    """
    if value is None or pd.isna(value):
        return ""

    num = _safe_number(value)
    if num is None:
        return str(value)

    if not math.isfinite(num):
        return str(value)

    format_str_clean = d3format.strip()
    if not format_str_clean:
        return str(value)

    # Handle duration formats
    duration_formats = {
        "DURATION",
        "DURATION_SECONDS",
        "DURATION_HMMSS",
        "DURATION_HMMSS_SECONDS",
    }
    if (d3format_upper := format_str_clean.upper()) in duration_formats:
        return format_duration_by_d3format(num, d3format_upper)

    # For other formats, return as string
    try:
        format_template = "{:" + format_str_clean + "}"
        formatted = format_template.format(num)

        if isinstance(formatted, str):
            formatted = (
                formatted.replace("\t", " ").replace("\n", " ").replace("\r", " ")
            )

        return formatted
    except (ValueError, KeyError, OverflowError):
        return str(value)


# pylint: disable=invalid-name,too-many-locals
def apply_export_as_time_after_translation(
    df: pd.DataFrame,
    form_data: dict[str, Any] | None = None,
    column_translations: dict[str, str] | None = None,
    stable_metric_mapping: dict[str, str] | None = None,
) -> pd.DataFrame:
    """
    Apply time formatting for export based on column_config (d3NumberFormat or exportAsTime).
    Supports both milliseconds and seconds for duration formats.
    Uses stable column mapping for 100% reliability.

    Args:
        df: DataFrame to format
        form_data: Form data containing column_config
        column_translations: Translation mapping for column names
        stable_metric_mapping: Stable mapping from metrics to DataFrame columns

    Returns:
        DataFrame with time formatting applied (H:MM:SS format)

    Supports:
    - DURATION (milliseconds) -> "1m 6s" or "2h 15m 30s"
    - DURATION_SECONDS (seconds) -> "1m 6s" or "2h 15m 30s"
    - DURATION_HMMSS (milliseconds) -> H:MM:SS
    - DURATION_HMMSS_SECONDS (seconds) -> H:MM:SS
    - exportAsTime flag (backward compatibility, treats as milliseconds)
    """
    form_data = form_data or {}
    column_translations = column_translations or {}
    column_config = form_data.get("column_config", {})

    if not column_config:
        return df

    metric_to_df_column = stable_metric_mapping or {}

    if column_translations and metric_to_df_column:
        updated_mapping = {}
        for original_metric, original_df_col in metric_to_df_column.items():
            if original_metric in column_translations:
                translated_name = column_translations[original_metric]
                updated_mapping[original_metric] = translated_name
            else:
                updated_mapping[original_metric] = original_df_col
        metric_to_df_column = updated_mapping

    if not metric_to_df_column:
        logger.warning(
            "Export Debug - No stable mapping available, using fallback mapping"
        )
        for df_col in df.columns:
            metric_to_df_column[df_col] = df_col

    for config_key, config_value in column_config.items():
        cfg = config_value.get("config") if isinstance(config_value, dict) else None
        effective_cfg = (
            cfg
            if isinstance(cfg, dict)
            else (config_value if isinstance(config_value, dict) else {})
        )

        # Get d3NumberFormat from config
        d3format = ""
        try:
            raw_fmt = effective_cfg.get("d3NumberFormat") or effective_cfg.get(
                "d3_number_format"
            )
            d3format = (raw_fmt or "").strip()
        except Exception:  # pylint: disable=broad-except
            d3format = ""

        # Check for backward compatibility with exportAsTime flag
        export_as_time = effective_cfg.get("exportAsTime", False)

        # Check if we need to apply time formatting
        d3format_upper = d3format.upper() if d3format else ""
        is_duration_format = "DURATION" in d3format_upper if d3format else False

        if not is_duration_format and not export_as_time:
            continue

        target_column = None

        if config_key in metric_to_df_column:
            target_column = metric_to_df_column[config_key]
        elif column_translations:
            for original_metric, translated_name in column_translations.items():
                if (
                    translated_name == config_key
                    and original_metric in metric_to_df_column
                ):
                    target_column = metric_to_df_column[original_metric]
                    break

        if target_column and isinstance(df.get(target_column), pd.Series):
            if is_duration_format:
                # Use format_duration_by_d3format for DURATION formats
                # Supports:
                # - DURATION / DURATION_SECONDS -> "1m 6s" or "2h 15m 30s"
                # - DURATION_HMMSS / DURATION_HMMSS_SECONDS -> H:MM:SS
                format_func = functools.partial(
                    format_duration_by_d3format, d3format=d3format
                )
                df[target_column] = df[target_column].apply(format_func)
            elif export_as_time:
                # Backward compatibility: use convert_to_time (always treats as milliseconds)
                df[target_column] = df[target_column].apply(convert_to_time)
        else:
            logger.warning(
                f"Export Debug - Could not find target column for config_key: {config_key}. "
                f"Available mappings: {list(metric_to_df_column.keys())}"
            )

    return df


# DODO added stop: CSV/XLSX export logic #52459208
