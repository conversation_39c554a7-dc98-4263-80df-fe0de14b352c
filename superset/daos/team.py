# DODO added #32839638
from __future__ import annotations

import logging
from typing import Any, Optional

from flask import g

# DODO added start: Team Carousel UI On Home page #52010498
from sqlalchemy.exc import SQLAlchemyError

# DODO added stop: Team Carousel UI On Home page #52010498
from superset.daos.base import <PERSON><PERSON><PERSON>
from superset.extensions import db, security_manager
from superset.models.team import Team

# DODO added start: Team Carousel UI On Home page #52010498
# DODO added stop: Team Carousel UI On Home page #52010498
from superset.utils.core import get_user_id

logger = logging.getLogger(__name__)


class TeamDAO(BaseDAO[Team]):
    @staticmethod
    def validate_slug_uniqueness(slug: str) -> bool:
        if not slug:
            return True
        team_query = db.session.query(Team).filter(Team.slug == slug)
        return not db.session.query(team_query.exists()).scalar()

    @staticmethod
    def find_team_by_slug(team_slug: str) -> Team:
        try:
            team = db.session.query(Team).filter(Team.slug == team_slug).one_or_none()
            return team
        except Exception:
            logger.warning("Cant find team by slug")
            raise

    @staticmethod
    def find_by_name(team_name: str) -> Team | None:
        """Find a team by name.

        :param team_name: The team name

        Returns:
            Team: The team if found, None otherwise
        """
        try:
            team = db.session.query(Team).filter(Team.name == team_name).one_or_none()
            return team
        except SQLAlchemyError as ex:
            logger.error("Failed to find team by name %s: %s", team_name, str(ex))
            return None

    @staticmethod
    def get_team_by_user_id(user_id: int | None = None) -> Optional[Team]:
        """Get user's team by user ID.

        :param user_id: Optional user id

        Returns:
            Optional[Team]: The user's team or None if not found
        """
        if user_id is None:
            user_id = get_user_id()
        try:
            user = (
                db.session.query(security_manager.user_model)
                .filter(security_manager.user_model.id == user_id)
                .one_or_none()
            )
            if not user or not user.teams:
                return None
            return user.teams[0]
        except SQLAlchemyError as ex:
            logger.error("Failed to get team for user %s: %s", user_id, str(ex))
            return None

    @staticmethod
    def get_team_ids_for_users(user_ids: set[int]) -> set[int]:
        """
        Given a set of user IDs, return IDs of all teams they belong to.

        :param user_ids: Set of user IDs
        :returns: Set of team IDs
        """
        if not user_ids:
            return set()

        try:
            rows = (
                db.session.query(Team.id)
                .join(Team.participants)
                .filter(security_manager.user_model.id.in_(user_ids))
                .distinct()
                .all()
            )
            return {team_id for (team_id,) in rows}
        except SQLAlchemyError as ex:
            logger.error("Failed to get team IDs for users %s: %s", user_ids, str(ex))
            return set()

    # DODO added start: Team Carousel UI On Home page #52010498
    @staticmethod
    def get_teams_with_tags_by_user_id(
        user_id: Optional[int] = None,
    ) -> list[dict[str, Any]]:
        """Get user's teams with their associated tag IDs.

        Args:
            user_id: User ID, if None uses current user

        Returns:
            List of dicts with team info and tag IDs
        """
        try:
            # Use the same approach as the working get_team_by_user_id method
            if user_id is None:
                user = g.user
            else:
                user = (
                    db.session.query(security_manager.user_model)
                    .filter(security_manager.user_model.id == user_id)
                    .one_or_none()
                )

            if not user or not user.teams:
                return []

            teams_with_tags = []
            for team in user.teams:
                # Use the tag_id directly from the team record
                team_info = {
                    "id": team.id,
                    "name": team.name,
                    "slug": team.slug,
                    "is_external": team.is_external,
                    "is_consumer": team.is_consumer,
                    "tag_id": team.tag_id,
                }
                teams_with_tags.append(team_info)

            return teams_with_tags
        except SQLAlchemyError as ex:
            logger.error(
                "Failed to get teams with tags for user %s: %s", user_id, str(ex)
            )
            return []

    # DODO added stop: Team Carousel UI On Home page #52010498
