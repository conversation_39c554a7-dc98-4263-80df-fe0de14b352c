import { SupersetClient } from '@superset-ui/core';
import rison from 'rison';
import { RoleWithDetails, UserSearchResult } from './types';

const SYSTEM_ROLES: Record<string, string> = {
  Admin: 'true',
  Alpha: 'true',
  API_ONLY: 'true',
  'Check data': 'true',
  clients_sensetive_data: 'true',
  'Create data': 'true',
  custdev: 'true',
  cvm: 'true',
  'fake role': 'true',
  'Folders global admin': 'true',
  'Folders plugin admin': 'true',
  'Folders team admin': 'true',
  Gamma: 'true',
  'Gamma [Не используем]': 'true',
  granter: 'true',
  'granter [не используем]': 'true',
  hr_analytics_focus_group: 'true',
  'Metadata API': 'true',
  Public: 'true',
  public_dashboards: 'true',
  'Public [Не используем]': 'true',
  readonly: 'true',
  'roles_admin [Не используем]': 'true',
  sql_lab: 'true',
  'sql_lab [Не используем]': 'true',
  support_admin: 'true',
  'Vizualize data': 'true',
};

export const loadRoles = async (
  input = '',
  page = 0,
  pageSize = 25,
): Promise<{ data: RoleWithDetails[]; totalCount: number }> => {
  const query = rison.encode({
    filter: input,
    page,
    page_size: pageSize,
  });

  try {
    const response = await SupersetClient.get({
      endpoint: `/api/v1/dashboard/related/roles?q=${query}`,
    });

    const roles = response.json.result
      .filter((item: { text: string; extra: { active: boolean } }) =>
        SYSTEM_ROLES[item.text] || item.extra.active !== undefined
          ? item.extra.active
          : true,
      )
      .map((item: { value: number; text: string }) => ({
        id: item.value,
        name: item.text,
      }));

    return {
      data: roles,
      totalCount: response.json.count,
    };
  } catch (error) {
    return { data: [], totalCount: 0 };
  }
};

/**
 * Получает детальную информацию о роли, включая связанную команду
 */
export const getTeamDetails = async (role: {
  id: number;
  name: string;
}): Promise<RoleWithDetails | null> => {
  const teamResponse = await SupersetClient.get({
    endpoint: `/api/v1/team/${role.name.toUpperCase()}`,
  });

  return {
    id: role.id,
    name: role.name,
    team: teamResponse.json.result,
  };
};

export const searchUsers = async (
  searchTerm: string,
  page = 0,
  pageSize = 25,
): Promise<{ data: UserSearchResult[]; totalCount: number }> => {
  const filterExps = [
    { col: 'first_name', opr: 'usr_name', value: searchTerm },
  ];
  const query = rison.encode_uri({
    filters: filterExps,
    page,
    page_size: pageSize,
  });

  try {
    const response = await SupersetClient.get({
      endpoint: `/api/v1/dodo_user/?q=${query}`,
    });

    const users = response.json.result.map((user: any) => ({
      id: user.id,
      username: user.username,
      first_name: user.first_name,
      last_name: user.last_name,
      email: user.email,
      is_active: user.is_active,
      team_name: user.teams?.[0]?.name,
      country_name: user.user_info?.country_name,
      dodo_role: user.user_info?.dodo_role,
      created_on: user.created_on,
      last_login: user.last_login,
      login_count: user.login_count,
    }));

    return {
      data: users,
      totalCount: response.json.count,
    };
  } catch (error) {
    return { data: [], totalCount: 0 };
  }
};
