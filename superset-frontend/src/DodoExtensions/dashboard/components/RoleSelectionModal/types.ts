import Role from 'src/types/Role';

interface User {
  id: number;
  username: string;
  first_name: string;
  last_name: string;
  email: string;
  is_active: boolean;
  created_on?: string;
  last_login?: string;
  login_count?: number;
}

interface Team {
  id: number;
  name: string;
  slug: string;
  is_external: boolean;
  roles: Role[];
  participants: User[];
}

export interface RoleWithDetails extends Role {
  team?: Team;
}

export interface UserSearchResult extends User {
  team_name?: string;
  country_name?: string;
  dodo_role?: string;
}

export interface RoleSelectionModalProps {
  visible: boolean;
  onCancel: () => void;
  onApply: (selectedRoles: Role[]) => void;
  selectedRoles: Role[];
}
