import { useState, useEffect, useCallback } from 'react';
import { t, styled } from '@superset-ui/core';
import Modal from 'src/components/Modal';
import Loading from 'src/components/Loading';
import Icons from 'src/components/Icons';
import Checkbox from 'src/components/Checkbox';
import Tabs from 'src/components/Tabs';
import { Input } from 'src/components/Input';
import { EmptyStateSmall } from 'src/components/EmptyState';
import { useToasts } from 'src/components/MessageToasts/withToasts';
import { loadRoles, getTeamDetails, searchUsers } from './api';
import {
  RoleSelectionModalProps,
  RoleWithDetails,
  UserSearchResult,
} from './types';

const { TabPane } = Tabs;

const StyledModal = styled(Modal)`
  .ant-modal-body {
    padding: 24px;
    max-height: 600px;
    overflow-y: auto;
  }
`;

const StyledTabs = styled(Tabs)`
  .ant-tabs-content-holder {
    max-height: 450px;
    overflow-y: auto;
  }
`;

const StyledRoleItem = styled.div`
  padding: ${({ theme }) => theme.gridUnit * 3}px;
  border: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
  border-radius: 6px;
  margin-bottom: ${({ theme }) => theme.gridUnit * 2}px;
  transition: all 0.2s;

  &:hover {
    border-color: ${({ theme }) => theme.colors.primary.base};
    background-color: ${({ theme }) => theme.colors.grayscale.light4};
  }

  &.selected {
    border-color: ${({ theme }) => theme.colors.primary.base};
    background-color: ${({ theme }) => theme.colors.primary.light4};
  }

  .role-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: ${({ theme }) => theme.gridUnit * 2}px;
    cursor: pointer;
  }

  .role-title {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }

  .show-details-button {
    margin-left: auto;
    padding: ${({ theme }) => theme.gridUnit * 1.5}px;
    font-size: ${({ theme }) => theme.typography.sizes.xs}px;
    /* color: ${({ theme }) => theme.colors.primary.base}; */
    cursor: pointer;
    background-color: ${({ theme }) => theme.colors.grayscale.light3};
    border-radius: 4px;
    border: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
    user-select: none;

    &:hover {
      background-color: ${({ theme }) => theme.colors.grayscale.light2};
    }
  }

  .team-details {
    margin-top: 8px;
  }

  .team-info {
    display: flex;
    align-items: center;
    gap: ${({ theme }) => theme.gridUnit}px;
    margin-bottom: ${({ theme }) => theme.gridUnit}px;
  }

  .team-members {
    max-height: 150px;
    overflow-y: auto;
    background-color: ${({ theme }) => theme.colors.grayscale.light5};
    border: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
    border-radius: 4px;
    padding: 8px;
  }

  .member-item {
    padding: 4px 0;
    border-bottom: 1px solid ${({ theme }) => theme.colors.grayscale.light3};

    &:last-child {
      border-bottom: none;
    }
  }

  .member-name {
    font-weight: 500;
    margin-bottom: 2px;
  }

  .member-details {
    font-size: 12px;
    color: ${({ theme }) => theme.colors.grayscale.base};
  }

  .error-message {
    color: ${({ theme }) => theme.colors.error.base};
    font-size: 12px;
  }
`;

const UserItem = styled.div`
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
  border-radius: 6px;
  margin-bottom: 8px;

  .user-header {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 8px;
  }

  .user-name {
    font-weight: 500;
    margin: 0;
  }

  .user-details {
    font-size: 12px;
    color: ${({ theme }) => theme.colors.grayscale.base};
    line-height: 1.4;
  }

  .team-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 2px 8px;
    background-color: ${({ theme }) => theme.colors.primary.light4};
    color: ${({ theme }) => theme.colors.primary.dark1};
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
  }
`;

const SearchInput = styled(Input)`
  margin-bottom: 16px;

  .ant-input-affix-wrapper {
    border-radius: 6px;
  }
`;

const ListContainer = styled.div`
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 40px 20px;
  color: ${({ theme }) => theme.colors.grayscale.base};
  font-style: italic;
`;

const RoleItem = ({
  role,
  isSelected,
  handleRoleToggle,
}: {
  role: RoleWithDetails;
  isSelected: boolean;
  handleRoleToggle: (role: RoleWithDetails) => void;
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [details, setDetails] = useState<RoleWithDetails | null>(null);
  const [isLoadingTeam, setIsLoadingTeam] = useState(false);
  const [error, setError] = useState('');
  const { addDangerToast } = useToasts();

  const loadTeamDetails = async () => {
    setIsLoadingTeam(true);
    try {
      const teamDetails = await getTeamDetails(role);
      setDetails(teamDetails);
    } catch (error) {
      const is400 = error?.status === 400;
      const errorMessage = is400
        ? t('Team not found')
        : t('Failed to load team details');
      addDangerToast(errorMessage);
      setError(errorMessage);

      if (isSelected) handleRoleToggle(role);
    } finally {
      setIsLoadingTeam(false);
    }
  };

  const handleShowDetails = () => {
    setShowDetails(prev => !prev);
    if (!details && !isLoadingTeam && !error) {
      loadTeamDetails();
    }
  };

  return (
    <StyledRoleItem key={role.id} className={isSelected ? 'selected' : ''}>
      <div
        className="role-header"
        role="button"
        tabIndex={0}
        onClick={() => (error ? undefined : handleRoleToggle(role))}
      >
        <Checkbox
          checked={isSelected}
          onChange={() => {}}
          disabled={!!error}
          className="role-checkbox"
        />
        <p className="role-title">{role.name}</p>
        <span
          role="button"
          tabIndex={0}
          onClick={e => {
            e.stopPropagation();
            handleShowDetails();
          }}
          aria-label={
            showDetails ? t('Hide team members') : t('Show team members')
          }
          className="show-details-button"
        >
          {showDetails ? t('Hide team members') : t('Show team members')}
        </span>
      </div>

      {showDetails && isLoadingTeam && <Loading position="inline-centered" />}
      {showDetails && error && <p className="error-message">{error}</p>}

      {showDetails && details?.team && (
        <div className="team-details">
          <div className="team-info">
            <Icons.TeamOutlined iconSize="m" />
            <span>{t('Team members')}:</span>
          </div>
          {details.team.participants.length > 0 && (
            <div className="team-members">
              {details.team.participants.map(member => (
                <div key={member.id} className="member-item">
                  <div className="member-name">
                    {member.first_name} {member.last_name}
                  </div>
                  <div className="member-details">
                    ID: {member.id} | Email: {member.email} | Username:{' '}
                    {member.username}
                  </div>
                </div>
              ))}
            </div>
          )}
          {details.team.participants.length === 0 && (
            <EmptyStateSmall title={t('No team members found')} />
          )}
        </div>
      )}
    </StyledRoleItem>
  );
};

const RoleSelectionModal: React.FC<RoleSelectionModalProps> = ({
  visible,
  onCancel,
  onApply,
  selectedRoles,
}) => {
  const [activeTab, setActiveTab] = useState('roles');
  const [roleSearchTerm, setRoleSearchTerm] = useState('');
  const [userSearchTerm, setUserSearchTerm] = useState('');
  const [roles, setRoles] = useState<RoleWithDetails[]>([]);
  const [users, setUsers] = useState<UserSearchResult[]>([]);
  const [selectedRoleIds, setSelectedRoleIds] = useState<Set<number>>(
    new Set(),
  );
  const [loading, setLoading] = useState(false);

  // Initialize selected roles
  useEffect(() => {
    setSelectedRoleIds(new Set(selectedRoles.map(role => role.id)));
  }, [selectedRoles]);

  // Load roles
  const loadRolesData = useCallback(async (searchTerm = '') => {
    setLoading(true);
    try {
      const { data } = await loadRoles(searchTerm, 0, 50);
      setRoles(data);
    } catch (error) {
      // Error handling
    } finally {
      setLoading(false);
    }
  }, []);

  // Search users
  const searchUsersData = useCallback(async (searchTerm: string) => {
    if (!searchTerm.trim()) {
      setUsers([]);
      return;
    }

    setLoading(true);
    try {
      const { data } = await searchUsers(searchTerm, 0, 20);
      setUsers(data);
    } catch (error) {
      // Error handling
    } finally {
      setLoading(false);
    }
  }, []);

  // Search roles
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (activeTab === 'roles') {
        loadRolesData(roleSearchTerm);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [roleSearchTerm, activeTab, loadRolesData]);

  // Search users
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (activeTab === 'users') {
        searchUsersData(userSearchTerm);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [userSearchTerm, activeTab, searchUsersData]);

  const handleRoleToggle = (role: RoleWithDetails) => {
    const newSelectedIds = new Set(selectedRoleIds);
    if (newSelectedIds.has(role.id)) {
      newSelectedIds.delete(role.id);
    } else {
      newSelectedIds.add(role.id);
    }
    setSelectedRoleIds(newSelectedIds);
  };

  const handleApply = () => {
    const selectedRolesList = roles.filter(role =>
      selectedRoleIds.has(role.id),
    );
    onApply(selectedRolesList);
  };

  const renderUserItem = (user: UserSearchResult) => (
    <UserItem key={user.id}>
      <div className="user-header">
        <Icons.User iconSize="m" />
        <p className="user-name">
          {user.first_name} {user.last_name}
        </p>
        {user.team_name && (
          <span className="team-badge">
            {t('Team: ')}
            {user.team_name}
          </span>
        )}
      </div>
      <div className="user-details">
        ID: {user.id} | Email: {user.email} | Username: {user.username}
        {user.country_name && <span> | Country: {user.country_name}</span>}
        {user.dodo_role && <span> | Role: {user.dodo_role}</span>}
      </div>
    </UserItem>
  );

  return (
    <StyledModal
      title={t('View access')}
      show={visible}
      onHide={onCancel}
      onHandledPrimaryAction={handleApply}
      primaryButtonName={`${t('Apply')} (${selectedRoleIds.size})`}
      width="800px"
      responsive
    >
      <StyledTabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={t('Select Teams')} key="roles">
          <SearchInput
            placeholder={t('Search teams...')}
            value={roleSearchTerm}
            onChange={e => setRoleSearchTerm(e.target.value)}
          />
          <ListContainer>
            {roles.map(role => (
              <RoleItem
                key={role.id}
                role={role}
                isSelected={selectedRoleIds.has(role.id)}
                handleRoleToggle={handleRoleToggle}
              />
            ))}
            {roles.length === 0 && !loading && (
              <EmptyState>
                <span>{t('No teams found')}</span>
              </EmptyState>
            )}
            {loading && <Loading position="inline-centered" />}
          </ListContainer>
        </TabPane>
        <TabPane tab={t('Search Users')} key="users">
          <SearchInput
            placeholder={t('Search users by name or ID...')}
            value={userSearchTerm}
            onChange={e => setUserSearchTerm(e.target.value)}
          />
          <ListContainer>
            {users.map(renderUserItem)}
            {users.length === 0 && userSearchTerm && !loading && (
              <EmptyState>
                <span>{t('No users found')}</span>
              </EmptyState>
            )}
            {!userSearchTerm && (
              <EmptyState>
                <span>{t('Enter a name or ID to search for users')}</span>
              </EmptyState>
            )}
          </ListContainer>
        </TabPane>
      </StyledTabs>
    </StyledModal>
  );
};

export default RoleSelectionModal;
