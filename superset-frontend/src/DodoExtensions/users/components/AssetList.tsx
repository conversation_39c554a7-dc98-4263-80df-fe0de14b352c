// DODO was here
import { useEffect, useMemo, useState } from 'react';
import { Link } from 'react-router-dom';
import { styled, t } from '@superset-ui/core';
import { useToasts } from 'src/components/MessageToasts/withToasts';
import FacePile from 'src/components/FacePile';
import { ModifiedInfo } from 'src/components/AuditInfo';
import Icons from 'src/components/Icons';
import { Tooltip } from 'src/components/Tooltip';
import PropertiesModal from 'src/explore/components/PropertiesModal';
import DashboardPropertiesModal from 'src/dashboard/components/PropertiesModal';
import ListView from 'src/components/ListView';
import { useListViewResource } from 'src/views/CRUD/hooks';
import type { FilterValue } from 'src/components/ListView/types';

const Wrapper = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;

  .list-view {
    flex: 1;
  }

  .pagination-container {
    position: sticky;
    bottom: 0;
    background-color: ${({ theme }) => theme.colors.grayscale.light5};
    margin: 0 !important;
    padding-bottom: ${({ theme }) => theme.gridUnit * 4}px;
  }
`;

const StyledActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.gridUnit * 2}px;

  .action-button {
    cursor: pointer;
    color: ${({ theme }) => theme.colors.grayscale.base};

    &:hover {
      color: ${({ theme }) => theme.colors.primary.base};
    }
  }
`;

interface AssetListProps {
  userId: number;
  assetType: 'dashboard' | 'chart' | 'dataset';
  onSelectionChange: (selectedIds: number[]) => void;
}

const AssetList = ({
  userId,
  assetType,
  onSelectionChange,
}: AssetListProps) => {
  const [editingAsset, setEditingAsset] = useState<any | null>(null);
  const [currentSelection, setCurrentSelection] = useState<number[]>([]);
  const { addDangerToast, addSuccessToast } = useToasts();

  // Base filter to only show assets owned by this user
  const baseFilters = useMemo<FilterValue[]>(
    () => [
      {
        id: 'owners',
        operator: 'rel_m_m',
        value: userId,
      },
    ],
    [userId],
  );

  const {
    state: {
      loading,
      resourceCount,
      resourceCollection: assets,
      bulkSelectEnabled,
    },
    fetchData,
    refreshData,
    toggleBulkSelect,
  } = useListViewResource(
    assetType,
    t(assetType),
    addDangerToast,
    true,
    [],
    baseFilters,
    false,
    undefined,
    true, // Enable bulk select by default
  );

  // Notify parent when selection changes
  useEffect(() => {
    onSelectionChange(currentSelection);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentSelection]);

  const handleEdit = (asset: any) => {
    setEditingAsset(asset);
  };

  const handleCloseEdit = () => {
    setEditingAsset(null);
  };

  const handleSaveEdit = () => {
    setEditingAsset(null);
    refreshData();
    addSuccessToast(t('Asset updated successfully'));
  };

  // Define columns based on asset type
  const columns = useMemo(() => {
    const nameKey =
      assetType === 'dashboard'
        ? 'dashboard_title'
        : assetType === 'chart'
          ? 'slice_name'
          : 'table_name';

    const getLink = (record: any) => {
      if (assetType === 'dashboard') return `/superset/dashboard/${record.id}/`;
      if (assetType === 'chart') return `/explore/?slice_id=${record.id}`;
      return `/tablemodelview/list/?_flt_0_id=${record.id}`;
    };

    const baseColumns = [
      {
        Cell: ({ row: { original } }: any) => (
          <Link to={getLink(original)} target="_blank">
            {original[nameKey]}
          </Link>
        ),
        Header: t('Name'),
        accessor: nameKey,
        size: 'xl',
      },
      {
        Cell: ({ row: { original } }: any) => original.id,
        Header: t('ID'),
        accessor: 'id',
        size: 'xs',
      },
      {
        Cell: ({ row: { original } }: any) => (
          <FacePile users={original.owners || []} />
        ),
        Header: t('Owners'),
        accessor: 'owners',
        disableSortBy: true,
        size: 'lg',
      },
      {
        Cell: ({ row: { original } }: any) => (
          <ModifiedInfo
            date={original.changed_on_delta_humanized}
            user={original.changed_by}
          />
        ),
        Header: t('Modified'),
        accessor: 'changed_on_delta_humanized',
        size: 'lg',
      },
      {
        Cell: ({ row: { original } }: any) => (
          <StyledActions>
            <Tooltip
              id="edit-action-tooltip"
              title={t('Edit')}
              placement="bottom"
            >
              <span
                role="button"
                tabIndex={0}
                className="action-button"
                onClick={() => handleEdit(original)}
              >
                <Icons.EditAlt data-test="edit-alt" />
              </span>
            </Tooltip>
          </StyledActions>
        ),
        Header: t('Actions'),
        accessor: 'actions',
        disableSortBy: true,
        size: 'sm',
      },
    ];

    // Add published column for dashboards
    if (assetType === 'dashboard') {
      baseColumns.splice(3, 0, {
        Cell: ({ row: { original } }: any) =>
          original.published ? t('Yes') : t('No'),
        Header: t('Published'),
        accessor: 'published',
        size: 'sm',
      });
    }

    return baseColumns;
  }, [assetType]);

  // Dummy bulk action (required for bulk select to work)
  const bulkActions = useMemo(
    () => [
      {
        key: 'hidden',
        name: '',
        onSelect: () => {},
        type: 'primary' as const,
      },
    ],
    [],
  );

  // Custom render for bulk select to track selections
  const renderBulkSelectCopy = (selected: any[]) => {
    // Store selection in state (will trigger useEffect to notify parent)
    const selectedIds = selected.map(row => row.original.id);
    if (JSON.stringify(selectedIds) !== JSON.stringify(currentSelection)) {
      // Use setTimeout to defer state update until after render
      setTimeout(() => setCurrentSelection(selectedIds), 0);
    }
    return t('%s Selected', selected.length);
  };

  return (
    <>
      <Wrapper>
        <ListView
          className="list-view"
          columns={columns}
          data={assets}
          count={resourceCount}
          pageSize={25}
          fetchData={fetchData}
          loading={loading}
          initialSort={[{ id: 'changed_on_delta_humanized', desc: true }]}
          bulkActions={bulkActions}
          bulkSelectEnabled={bulkSelectEnabled}
          disableBulkSelect={toggleBulkSelect}
          renderBulkSelectCopy={renderBulkSelectCopy}
          refreshData={refreshData}
          addSuccessToast={addSuccessToast}
          addDangerToast={addDangerToast}
          defaultViewMode="table"
        />
      </Wrapper>
      {editingAsset && assetType === 'chart' && (
        <PropertiesModal
          show
          onHide={handleCloseEdit}
          onSave={handleSaveEdit}
          slice={{
            slice_id: editingAsset.id,
            slice_name: editingAsset.slice_name,
            description: editingAsset.description,
            cache_timeout: editingAsset.cache_timeout,
            certified_by: editingAsset.certified_by,
            certification_details: editingAsset.certification_details,
            is_managed_externally: editingAsset.is_managed_externally,
          }}
          addSuccessToast={addSuccessToast}
        />
      )}
      {editingAsset && assetType === 'dashboard' && (
        <DashboardPropertiesModal
          show
          dashboardId={editingAsset.id}
          dashboardTitle={editingAsset.dashboard_title}
          dashboardTitleRu={editingAsset.dashboard_title_ru}
          onHide={handleCloseEdit}
          addSuccessToast={addSuccessToast}
          addDangerToast={addDangerToast}
        />
      )}
    </>
  );
};

export default AssetList;
