import { Link } from 'react-router-dom';
import { styled, t, useTheme } from '@superset-ui/core';
import withToasts from 'src/components/MessageToasts/withToasts';
import ListView from 'src/components/ListView/ListView';
import { Tooltip } from 'src/components/Tooltip';
import { useListViewResource } from 'src/views/CRUD/hooks';
import { FilterOperator, Filters } from 'src/components/ListView/types';
import SubMenu from 'src/features/home/<USER>';
import { OBJECTIVE_ROLES } from './consts';

const TruncatedText = styled.span`
  display: inline-block;
  overflow: hidden;
  max-width: 100px;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const initialSort = [{ id: 'id', desc: false }];

const filters: Filters = [
  {
    id: 'id',
    Header: 'ID',
    key: 'id',
    input: 'search',
    operator: 'eq_id_user' as FilterOperator,
  },
  {
    id: 'first_name',
    Header: t('First name'),
    key: 'first_name',
    input: 'search',
    operator: 'usr_name' as FilterOperator,
  },
  {
    id: 'last_name',
    Header: t('Last name'),
    key: 'last_name',
    input: 'search',
    operator: 'usr_last_name' as FilterOperator,
  },
  {
    id: 'email',
    Header: t('Email'),
    key: 'email',
    input: 'search',
    operator: 'usr_email' as FilterOperator,
  },
  {
    id: 'username',
    Header: t('Username'),
    key: 'username',
    input: 'search',
    operator: 'usr_username' as FilterOperator,
  },
  {
    id: 'active',
    Header: t('Active'),
    key: 'active',
    input: 'select',
    operator: FilterOperator.Equals,
    unfilteredLabel: t('All'),
    selects: [
      { label: t('Yes'), value: true },
      { label: t('No'), value: false },
    ],
  },
  {
    id: 'user_info',
    Header: t('MC'),
    key: 'is_management',
    input: 'select',
    operator: FilterOperator.UserIsFromManagemnetCompany,
    unfilteredLabel: t('All'),
    selects: [
      { label: t('Yes'), value: true },
      { label: t('No'), value: false },
    ],
  },
];

interface UserData {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  username: string;
  is_active: boolean;
  created_on: string;
  last_login?: string;
  login_count: number;
  objective_roles: { id: number; role: number }[];
  teams: Array<{ name: string }>;
  user_info: {
    country_name?: string;
    dodo_role?: string;
    is_onboarding_finished?: boolean;
    is_management: boolean;
  } | null;
  roles: Array<{ name: string }>;
}

const UserList = ({
  addDangerToast,
  addSuccessToast,
}: {
  addDangerToast: (msg: string) => void;
  addSuccessToast: (msg: string) => void;
}) => {
  const {
    state: { loading, resourceCount, resourceCollection },
    fetchData,
    refreshData,
  } = useListViewResource<UserData>(
    'dodo_user',
    t('Users'),
    addDangerToast,
    undefined,
  );

  const theme = useTheme();

  const columns = [
    // {
    //   id: 'id',
    //   Cell: ({ value }: { value: number }) => (
    //     <Link to={`/users/list/?_flt_0_id=${value}`} target="_blank">
    //       {value}
    //     </Link>
    //   ),
    //   Header: 'ID',
    //   accessor: 'id',
    //   size: 'xs',
    //   sortable: true,
    // },
    {
      id: 'id',
      Cell: ({ value }: { value: number }) => (
        <Link to={`/dodo_user/${value}`}>{value}</Link>
      ),
      Header: 'ID',
      accessor: 'id',
      size: 'xs',
      sortable: true,
    },
    {
      id: 'first_name',
      Header: t('First name'),
      accessor: 'first_name',
      sortable: true,
    },
    {
      id: 'last_name',
      Header: t('Last name'),
      accessor: 'last_name',
      sortable: true,
    },
    {
      id: 'email',
      Header: t('Email'),
      accessor: 'email',
      sortable: true,
    },
    {
      id: 'username',
      Cell: ({ value }: { value: string }) => (
        <Tooltip title={value} placement="bottom">
          <TruncatedText>{value}</TruncatedText>
        </Tooltip>
      ),
      Header: t('Username'),
      accessor: 'username',
      sortable: true,
    },
    {
      id: 'roles',
      Cell: ({ value }: { value: Array<{ name: string }> }) => {
        const roleName =
          value?.map(item => item.name).join(', ') || t('Not assigned');
        return (
          <Tooltip title={roleName} placement="bottom">
            <TruncatedText>{roleName}</TruncatedText>
          </Tooltip>
        );
      },
      Header: t('Role'),
      accessor: 'roles',
      disableSortBy: true,
    },
    {
      id: 'active',
      key: 'active',
      Cell: ({ value }: { value: boolean }) => (
        <span
          style={{
            color: value ? theme.colors.success.base : theme.colors.error.base,
          }}
        >
          {value ? t('Active') : t('Inactive')}
        </span>
      ),
      Header: t('Status'),
      accessor: 'is_active',
      disableSortBy: true,
      size: 'xs',
    },
    {
      id: 'objective_roles',
      Cell: ({ value }: { value: Array<{ role: number }> }) => {
        const objectiveRoleName =
          value
            ?.map(item => OBJECTIVE_ROLES[item.role] || t('Unknown role'))
            .join(', ') || t('Not assigned');
        return (
          <Tooltip title={objectiveRoleName} placement="bottom">
            <TruncatedText>{objectiveRoleName}</TruncatedText>
          </Tooltip>
        );
      },
      Header: t('Dodois roles'),
      accessor: 'objective_roles',
      disableSortBy: true,
    },
    {
      id: 'teams',
      Cell: ({ value }: { value: Array<{ name: string }> }) => {
        const teamName = value?.[0]?.name || t('Not assigned');
        return (
          <Tooltip title={teamName} placement="bottom">
            <TruncatedText>{teamName}</TruncatedText>
          </Tooltip>
        );
      },
      Header: t('Team'),
      accessor: 'teams',
      disableSortBy: true,
    },
    {
      id: 'dodo_role',
      Cell: ({ row }: { row: { original: UserData } }) => {
        const dodoRole = row.original.user_info?.dodo_role || t('Not assigned');
        return <span>{dodoRole}</span>;
      },
      Header: t('Dodo role'),
      accessor: 'user_info.dodo_role',
      disableSortBy: true,
    },
    {
      id: 'user_info',
      Cell: ({ row }: { row: { original: UserData } }) => {
        const isManagement = row.original.user_info?.is_management;
        return isManagement ? t('Yes') : t('No');
      },
      Header: t('MC'),
      accessor: 'user_info.is_management',
      disableSortBy: true,
    },
    // {
    //   id: 'country_name',
    //   Cell: ({ row }: { row: { original: UserData } }) => {
    //     const countryName =
    //       row.original.user_info?.country_name || t('Unknown');
    //     return <span>{countryName}</span>;
    //   },
    //   Header: t('Country'),
    //   accessor: 'user_info.country_name',
    //   disableSortBy: true,
    // },
    // {
    //   id: 'onboarding_status',
    //   Cell: ({ row }: { row: { original: UserData } }) => {
    //     const isFinished = row.original.user_info?.is_onboarding_finished;
    //     return (
    //       <span
    //         style={{
    //           color: isFinished
    //             ? theme.colors.success.base
    //             : theme.colors.error.base,
    //         }}
    //       >
    //         {isFinished ? t('Completed') : t('Incompleted')}
    //       </span>
    //     );
    //   },
    //   Header: t('Onboarding'),
    //   accessor: 'user_info.is_onboarding_finished',
    //   disableSortBy: true,
    // },
    {
      id: 'last_login',
      Cell: ({ value }: { value?: string }) => {
        if (!value) return '-';
        const date = new Date(value);
        return date.toLocaleDateString();
      },
      Header: t('Last Login'),
      accessor: 'last_login',
      sortable: true,
    },
    {
      id: 'login_count',
      Cell: ({ value }: { value: number }) => value || 0,
      Header: t('Login Count'),
      accessor: 'login_count',
      sortable: true,
      size: 'xs',
    },
  ];

  return (
    <>
      <SubMenu name={t('Users')} />
      <ListView<UserData>
        columns={columns}
        filters={filters}
        initialSort={initialSort}
        data={resourceCollection}
        count={resourceCount}
        pageSize={25}
        fetchData={fetchData}
        refreshData={refreshData}
        loading={loading}
        addDangerToast={addDangerToast}
        addSuccessToast={addSuccessToast}
        defaultViewMode="table"
      />
    </>
  );
};

export default withToasts(UserList);
