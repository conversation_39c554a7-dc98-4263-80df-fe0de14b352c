import { byIso } from 'country-code-lookup';
import { styled, t } from '@superset-ui/core';
import Label from 'src/components/Label';

const Wrapper = styled.span`
  display: flex;
  align-items: center;
`;

const Text = styled.span`
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: ${({ theme }) => theme.gridUnit}px;
`;

const StyledLabel = styled(Label)`
  overflow: visible;
  margin-right: ${({ theme }) => theme.gridUnit}px;
  padding: 0.1em 0.5em;
`;

export const getCountryName = (
  country_num: number | null,
  country_name: string | null,
) => {
  const country = country_num ? byIso(country_num) : null;
  return country ? country.iso2 : country_name;
};

export const createUserSelectLabel = (
  item: any,
  fromExtra: boolean,
  asNode = true,
) => {
  if (!item) return '';

  if (item.label) return item.label;
  if (item.full_name) return item.full_name;

  const name = fromExtra ? item.text : `${item.first_name} ${item.last_name}`;
  const email = fromExtra ? item.extra.email : item.email;

  const country_name = fromExtra ? item.extra.country_name : item.country_name;
  const country_num = fromExtra ? item.extra.country_num : item.country_num;
  const country = getCountryName(country_num, country_name);

  const is_management = fromExtra
    ? item.extra.is_management
    : item.is_management;
  const managementText = is_management ? t('MC') : t('non-MC');

  const title = `${name}${email ? `, ${email}` : ''}`;

  const hasManagement = fromExtra
    ? 'is_management' in item.extra
    : 'is_management' in item;

  if (!asNode) {
    let label = title;
    if (country) label += ` (${country})`;
    if (hasManagement) label += ` (${managementText})`;
    return label;
  }

  return (
    <Wrapper title={item.text}>
      <Text title={title}>
        {name}
        {email ? `, ${email}` : ''}
      </Text>
      {country && (
        <StyledLabel
          css={theme => ({ backgroundColor: theme.colors.grayscale.light2 })}
        >
          {country}
        </StyledLabel>
      )}
      {hasManagement && (
        <StyledLabel type={is_management ? 'success' : 'link'}>
          {managementText}
        </StyledLabel>
      )}
    </Wrapper>
  );
};
