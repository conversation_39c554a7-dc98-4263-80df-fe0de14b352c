// DODO was here
import Owner from 'src/types/Owner';
import { t } from '@superset-ui/core';
// DODO added 58303552
import { getCountryName } from 'src/DodoExtensions/utils/createUserSelectLabel';

export default function getOwnerName(
  owner?: Owner & { label?: string },
): string {
  if (!owner) {
    return '';
  }

  if (owner.label) {
    return owner.label;
  }
  // DODO changed 44211759
  let name = owner.full_name || `${owner.first_name} ${owner.last_name}`;

  // DODO added start 44211759
  const country = getCountryName(
    owner.country_num || null,
    owner.country_name || null,
  );
  if (country) name += ` (${country})`;

  // DODO added 58303552
  if ('is_management' in owner) {
    name += ` (${owner.is_management ? t('MC') : t('non-MC')})`;
  }

  if (owner.email) name += ` ${owner.email}`;
  // DODO added stop 44211759

  return name;
}
