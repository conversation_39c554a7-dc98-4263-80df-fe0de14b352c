// DODO was here

/**
 * The Owner model as returned from the API
 */

// DODO added 44211759
interface OwnerDodoExtened {
  email?: string;
  country_name?: string;
  country_num?: number; // DODO added 58303552
  is_management?: boolean; // DODO added 58303552
  team?: string;
}
export default interface Owner extends OwnerDodoExtened {
  first_name?: string;
  id: number;
  last_name?: string;
  full_name?: string;
}
