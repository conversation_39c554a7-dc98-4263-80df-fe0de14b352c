import { ControlSetItem } from '@superset-ui/chart-controls';
import { t } from '@superset-ui/core';

export const xShowSplitLine: ControlSetItem = {
  name: 'xShowSplitLine',
  config: {
    type: 'CheckboxControl',
    label: t('Split Line'),
    renderTrigger: true,
    default: false,
    description: t('Draw split lines for %s-axis ticks', 'x'),
  },
};

export const yShowSplitLine: ControlSetItem = {
  name: 'yShowSplitLine',
  config: {
    type: 'CheckboxControl',
    label: t('Split Line'),
    renderTrigger: true,
    default: true,
    description: t('Draw split lines for %s-axis ticks', 'y'),
  },
};

export const showYAxis: ControlSetItem = {
  name: 'showYAxis',
  config: {
    type: 'CheckboxControl',
    label: t('Show Y Axis'),
    renderTrigger: true,
    default: true,
    description: t('Show Y Axis'),
  },
};

export const ySplitLineOpacity: ControlSetItem = {
  name: 'ySplitLineOpacity',
  config: {
    type: 'SliderControl',
    label: t('Split Line Opacity'),
    renderTrigger: true,
    min: 0,
    max: 1,
    step: 0.1,
    default: 1,
    description: t('Opacity of split lines'),
  },
};

export const textBorderWidth: ControlSetItem = {
  name: 'textBorderWidth',
  config: {
    type: 'SliderControl',
    label: t('Text Border Width'),
    renderTrigger: true,
    min: 0,
    max: 5,
    step: 1,
    default: 0,
    description: t('Change text border width of chart values'),
  },
};
