FROM acrdodo.azurecr.io/superset-frontend:latest AS frontend

FROM apache/superset:4.1.1

USER root

ARG GECKODRIVER_VERSION=v0.34.0 \
    FIREFOX_VERSION=125.0.3

# Set environment variable for Playwright
ENV PLAYWRIGHT_BROWSERS_PATH=/usr/local/share/playwright-browsers

# Install requirements
COPY dodo_build/requirements.txt /app/requirements/
RUN pip install --no-cache-dir --upgrade uv
RUN --mount=type=cache,target=/root/.cache/uv \
    apt-get update -qq && apt-get install -yqq --no-install-recommends \
      build-essential pkg-config \
    && uv pip install --system -r /app/requirements/requirements.txt \
    && playwright install-deps \
    && PLAYWRIGHT_BROWSERS_PATH=/usr/local/share/playwright-browsers playwright install chromium \
    && apt-get autoremove -yqq --purge build-essential pkg-config \
    && rm -rf /var/[log,tmp]/* /tmp/* /var/lib/apt/lists/*

# Add configuration
COPY dodo_build/configs/ /app/configs/

# Add bootstrap commands
COPY dodo_build/docker /usr/bin/

# Add our superset code
RUN rm -rf /app/superset/
COPY superset/ /app/superset/

RUN rm -rf /app/superset/static/assets/
RUN rm -rf /app/superset-frontend/

COPY --from=frontend /app/superset/static/assets/ /app/superset/static/assets/

USER superset
